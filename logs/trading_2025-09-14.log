14:19:03 [api] INFO: TRADE: SmartAPI client initialized successfully (attempt 1)
14:19:03 [api] ERROR: No instrument files found
14:19:03 [instrument_updater] ERROR: Error downloading instruments from API: 'SmartConnect' object has no attribute 'getInstruments'
14:19:07 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:19:07 [main] INFO: TRADE: 🚀 Enhanced Algo Trading Bot Starting...
14:19:07 [main] INFO: TRADE: Trading Mode: SIMULATION
14:19:07 [main] INFO: TRADE: Default Lot Size: 1
14:19:07 [main] INFO: TRADE: Signal Timeout: 30s
14:19:07 [main] INFO: TRADE: ✅ Bot authenticated as <PERSON><PERSON><PERSON>ya_S_R
14:19:07 [main] INFO: TRADE: 🔍 Searching for group: ಆತ್ಮ ನಿರ್ಭರ
14:19:08 [main] INFO: TRADE: ✅ Found target group: ಆತ್ಮ ನಿರ್ಭರ
14:19:08 [main] INFO: TRADE: 🔄 <PERSON><PERSON> is now listening for trading signals...
14:21:40 [api] INFO: TRADE: SmartAPI client initialized successfully (attempt 1)
14:21:40 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:21:40 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:21:40 [main] INFO: TRADE: 🚀 Enhanced Algo Trading Bot Starting...
14:21:40 [main] INFO: TRADE: Trading Mode: SIMULATION
14:21:40 [main] INFO: TRADE: Default Lot Size: 1
14:21:40 [main] INFO: TRADE: Signal Timeout: 30s
14:21:41 [main] INFO: TRADE: ✅ Bot authenticated as Kaundinya_S_R
14:21:41 [main] INFO: TRADE: 🔍 Searching for group: ಆತ್ಮ ನಿರ್ಭರ
14:21:41 [main] INFO: TRADE: ✅ Found target group: ಆತ್ಮ ನಿರ್ಭರ
14:21:41 [main] INFO: TRADE: 🔄 Bot is now listening for trading signals...
14:23:13 [api] INFO: TRADE: SmartAPI client initialized successfully (attempt 1)
14:23:13 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:23:13 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:23:13 [main] INFO: TRADE: 🚀 Enhanced Algo Trading Bot Starting...
14:23:13 [main] INFO: TRADE: Trading Mode: SIMULATION
14:23:13 [main] INFO: TRADE: Default Lot Size: 1
14:23:13 [main] INFO: TRADE: Signal Timeout: 30s
14:23:14 [main] INFO: TRADE: ✅ Bot authenticated as Kaundinya_S_R
14:23:14 [main] INFO: TRADE: 🔍 Searching for group: ಆತ್ಮ ನಿರ್ಭರ
14:23:14 [main] INFO: TRADE: ✅ Found target group: ಆತ್ಮ ನಿರ್ಭರ
14:23:14 [main] INFO: TRADE: 🔄 Bot is now listening for trading signals...
14:25:13 [api] INFO: TRADE: SmartAPI client initialized successfully (attempt 1)
14:25:13 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:25:13 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:25:13 [main] INFO: TRADE: 🚀 Enhanced Algo Trading Bot Starting...
14:25:13 [main] INFO: TRADE: Trading Mode: SIMULATION
14:25:13 [main] INFO: TRADE: Default Lot Size: 1
14:25:13 [main] INFO: TRADE: Signal Timeout: 30s
14:25:14 [main] INFO: TRADE: ✅ Bot authenticated as Kaundinya_S_R
14:25:14 [main] INFO: TRADE: 🔍 Searching for group: ಆತ್ಮ ನಿರ್ಭರ
14:25:14 [main] INFO: TRADE: ✅ Found target group: ಆತ್ಮ ನಿರ್ಭರ
14:25:14 [main] INFO: TRADE: 🔄 Bot is now listening for trading signals...
14:25:38 [api] INFO: TRADE: SmartAPI client initialized successfully (attempt 1)
14:25:39 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:25:39 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:25:39 [main] INFO: TRADE: 🚀 Enhanced Algo Trading Bot Starting...
14:25:39 [main] INFO: TRADE: Trading Mode: SIMULATION
14:25:39 [main] INFO: TRADE: Default Lot Size: 1
14:25:39 [main] INFO: TRADE: Signal Timeout: 30s
14:25:39 [main] INFO: TRADE: ✅ Bot authenticated as Kaundinya_S_R
14:25:39 [main] INFO: TRADE: 🔍 Searching for group: ಆತ್ಮ ನಿರ್ಭರ
14:25:40 [main] INFO: TRADE: ✅ Found target group: ಆತ್ಮ ನಿರ್ಭರ
14:25:40 [main] INFO: TRADE: 🔄 Bot is now listening for trading signals...
14:26:50 [api] INFO: TRADE: SmartAPI client initialized successfully (attempt 1)
14:26:50 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:26:50 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:26:50 [main] INFO: TRADE: 🚀 Enhanced Algo Trading Bot Starting...
14:26:50 [main] INFO: TRADE: Trading Mode: SIMULATION
14:26:50 [main] INFO: TRADE: Default Lot Size: 1
14:26:50 [main] INFO: TRADE: Signal Timeout: 30s
14:26:51 [main] INFO: TRADE: ✅ Bot authenticated as Kaundinya_S_R
14:26:51 [main] INFO: TRADE: 🔍 Searching for group: ಆತ್ಮ ನಿರ್ಭರ
14:26:51 [main] INFO: TRADE: ✅ Found target group: ಆತ್ಮ ನಿರ್ಭರ
14:26:51 [main] INFO: TRADE: 🔄 Bot is now listening for trading signals...
14:29:26 [api] INFO: TRADE: ✅ SmartAPI client initialized successfully (attempt 1)
14:29:27 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:29:27 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:29:27 [trade] INFO: TRADE: ✅ SmartAPI connection verified successfully
14:29:27 [trade] INFO: TRADE: 💰 Available Cash: ₹0.2900
14:29:27 [main] INFO: TRADE: 🚀 Enhanced Algo Trading Bot Starting...
14:29:27 [main] INFO: TRADE: Trading Mode: SIMULATION
14:29:27 [main] INFO: TRADE: Default Lot Size: 1
14:29:27 [main] INFO: TRADE: Signal Timeout: 30s
14:29:28 [main] INFO: TRADE: ✅ Bot authenticated as Kaundinya_S_R
14:29:28 [main] INFO: TRADE: 🔍 Searching for group: ಆತ್ಮ ನಿರ್ಭರ
14:29:29 [main] INFO: TRADE: ✅ Found target group: ಆತ್ಮ ನಿರ್ಭರ
14:29:29 [main] INFO: TRADE: 🔄 Bot is now listening for trading signals...
14:30:40 [trade] ERROR: Trading status check failed: name 'config' is not defined
14:33:10 [api] INFO: TRADE: ✅ SmartAPI client initialized successfully (attempt 1)
14:33:11 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:33:11 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:33:11 [trade] INFO: TRADE: ✅ SmartAPI connection verified successfully
14:33:11 [trade] INFO: TRADE: 💰 Available Cash: ₹0.2900
14:33:11 [main] INFO: TRADE: 🚀 Enhanced Algo Trading Bot Starting...
14:33:11 [main] INFO: TRADE: Trading Mode: SIMULATION
14:33:11 [main] INFO: TRADE: Default Lot Size: 1
14:33:11 [main] INFO: TRADE: Signal Timeout: 30s
14:33:12 [main] INFO: TRADE: ✅ Bot authenticated as Kaundinya_S_R
14:33:12 [main] INFO: TRADE: 🔍 Searching for group: ಆತ್ಮ ನಿರ್ಭರ
14:33:12 [main] INFO: TRADE: ✅ Found target group: ಆತ್ಮ ನಿರ್ಭರ
14:33:12 [main] INFO: TRADE: 🔄 Bot is now listening for trading signals...
14:35:40 [trade] ERROR: Trading status check failed: name 'config' is not defined
14:37:01 [api] INFO: TRADE: ✅ SmartAPI client initialized successfully (attempt 1)
14:37:01 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:37:01 [api] INFO: TRADE: Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
14:37:01 [trade] INFO: TRADE: ✅ SmartAPI connection verified successfully
14:37:01 [trade] INFO: TRADE: 💰 Available Cash: ₹0.2900
14:37:01 [main] INFO: TRADE: 🚀 Enhanced Algo Trading Bot Starting...
14:37:01 [main] INFO: TRADE: Trading Mode: SIMULATION
14:37:01 [main] INFO: TRADE: Default Lot Size: 1
14:37:01 [main] INFO: TRADE: Signal Timeout: 30s
14:37:02 [main] INFO: TRADE: ✅ Bot authenticated as Kaundinya_S_R
14:37:02 [main] INFO: TRADE: 🔍 Searching for group: ಆತ್ಮ ನಿರ್ಭರ
14:37:02 [main] INFO: TRADE: ✅ Found target group: ಆತ್ಮ ನಿರ್ಭರ
14:37:02 [main] INFO: TRADE: 🔄 Bot is now listening for trading signals...
14:40:40 [trade] ERROR: Trading status check failed: name 'config' is not defined
14:42:02 [trade] ERROR: Trading status check failed: name 'config' is not defined
14:44:40 [api] INFO: TRADE: ✅ SmartAPI client initialized successfully (attempt 1)
14:44:41 [api] INFO: TRADE: ✅ Loaded 127276 instruments from all_instrument 2025-09-14.csv
14:44:41 [api] INFO: TRADE: ✅ Loaded 127276 instruments from all_instrument 2025-09-14.csv
14:44:41 [trade] INFO: TRADE: ✅ SmartAPI connection verified successfully
14:44:41 [trade] INFO: TRADE: 💰 Available Cash: ₹0.2900
14:44:41 [main] INFO: TRADE: 🚀 Enhanced Algo Trading Bot Starting...
14:44:41 [main] INFO: TRADE: Trading Mode: SIMULATION
14:44:41 [main] INFO: TRADE: Default Lot Size: 1
14:44:41 [main] INFO: TRADE: Signal Timeout: 30s
14:44:42 [main] INFO: TRADE: ✅ Bot authenticated as Kaundinya_S_R
14:44:42 [main] INFO: TRADE: 🔍 Searching for group: ಆತ್ಮ ನಿರ್ಭರ
14:44:42 [main] INFO: TRADE: ✅ Found target group: ಆತ್ಮ ನಿರ್ಭರ
14:44:42 [main] INFO: TRADE: 🔄 Bot is now listening for trading signals...
14:45:40 [trade] ERROR: Trading status check failed: name 'config' is not defined
14:49:42 [trade] ERROR: Trading status check failed: name 'config' is not defined
14:50:48 [api] INFO: TRADE: ✅ SmartAPI client initialized successfully (attempt 1)
14:50:48 [api] INFO: TRADE: ✅ Loaded 127276 instruments from smartapi_instruments_2025-09-14.csv
14:50:48 [api] INFO: TRADE: ✅ Loaded 127276 instruments from smartapi_instruments_2025-09-14.csv
14:50:49 [trade] INFO: TRADE: ✅ SmartAPI connection verified successfully
14:50:49 [trade] INFO: TRADE: 💰 Available Cash: ₹0.2900
14:50:49 [main] INFO: TRADE: 🚀 Enhanced Algo Trading Bot Starting...
14:50:49 [main] INFO: TRADE: Trading Mode: SIMULATION
14:50:49 [main] INFO: TRADE: Default Lot Size: 1
14:50:49 [main] INFO: TRADE: Signal Timeout: 30s
14:50:49 [main] INFO: TRADE: ✅ Bot authenticated as Kaundinya_S_R
14:50:49 [main] INFO: TRADE: 🔍 Searching for group: ಆತ್ಮ ನಿರ್ಭರ
14:50:49 [main] INFO: TRADE: ✅ Found target group: ಆತ್ಮ ನಿರ್ಭರ
14:50:49 [main] INFO: TRADE: 🔄 Bot is now listening for trading signals...
14:59:38 [api] INFO: TRADE: ✅ SmartAPI client initialized successfully (attempt 1)
14:59:39 [api] INFO: TRADE: ✅ Loaded 127276 instruments from smartapi_instruments_2025-09-14.csv
15:00:13 [api] INFO: TRADE: ✅ SmartAPI client initialized successfully (attempt 1)
15:00:13 [api] INFO: TRADE: ✅ Loaded 127276 instruments from smartapi_instruments_2025-09-14.csv
15:00:37 [api] INFO: TRADE: ✅ SmartAPI client initialized successfully (attempt 1)
15:00:38 [api] INFO: TRADE: ✅ Loaded 127276 instruments from smartapi_instruments_2025-09-14.csv
15:00:38 [api] INFO: TRADE: ✅ Loaded 127276 instruments from smartapi_instruments_2025-09-14.csv
15:00:38 [trade] INFO: TRADE: ✅ SmartAPI connection verified successfully
15:00:38 [trade] INFO: TRADE: 💰 Available Cash: ₹0.2900
15:00:38 [main] INFO: TRADE: 🚀 Enhanced Algo Trading Bot Starting...
15:00:38 [main] INFO: TRADE: Trading Mode: SIMULATION
15:00:38 [main] INFO: TRADE: Default Lot Size: 1
15:00:38 [main] INFO: TRADE: Signal Timeout: 30s
15:00:39 [main] INFO: TRADE: ✅ Bot authenticated as Kaundinya_S_R
15:00:39 [main] INFO: TRADE: 🔍 Searching for group: ಆತ್ಮ ನಿರ್ಭರ
15:00:39 [main] INFO: TRADE: ✅ Found target group: ಆತ್ಮ ನಿರ್ಭರ
15:00:39 [main] INFO: TRADE: 🔄 Bot is now listening for trading signals...
15:04:39 [api] INFO: TRADE: ✅ SmartAPI client initialized successfully (attempt 1)
15:04:39 [api] INFO: TRADE: ✅ Loaded 127276 instruments from smartapi_instruments_2025-09-14.csv
15:04:40 [api] INFO: TRADE: ✅ Loaded 127276 instruments from smartapi_instruments_2025-09-14.csv
15:04:40 [trade] INFO: TRADE: ✅ SmartAPI connection verified successfully
15:04:40 [trade] INFO: TRADE: 💰 Available Cash: ₹0.2900
15:04:40 [main] INFO: TRADE: 🚀 Enhanced Algo Trading Bot Starting...
15:04:40 [main] INFO: TRADE: Trading Mode: SIMULATION
15:04:40 [main] INFO: TRADE: Default Lot Size: 1
15:04:40 [main] INFO: TRADE: Signal Timeout: 30s
15:04:40 [main] INFO: TRADE: ✅ Bot authenticated as Kaundinya_S_R
15:04:40 [main] INFO: TRADE: 🔍 Searching for group: ಆತ್ಮ ನಿರ್ಭರ
15:04:40 [main] INFO: TRADE: ✅ Found target group: ಆತ್ಮ ನಿರ್ಭರ
15:04:40 [main] INFO: TRADE: 🔄 Bot is now listening for trading signals...
15:05:03 [api] WARNING: No instrument found for signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 53, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=155.4, stop_loss=151.2, entry_price=155.4, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
15:05:03 [trading_dataframes] WARNING: Instrument not found for NIFTY
15:05:03 [api] WARNING: No instrument found for signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 53, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=155.4, stop_loss=151.2, entry_price=155.4, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
15:05:03 [trade] ERROR: API signal processing failed: {'status': 'error', 'message': 'Instrument not found for NIFTY'}
15:05:03 [main] INFO: TRADE: ✅ Dataframe: Trade entry created for NIFTY 16 SEP 25000 CALL | API: Instrument not found for NIFTY
15:07:18 [api] INFO: TRADE: ✅ SmartAPI client initialized successfully (attempt 1)
15:07:18 [api] INFO: TRADE: ✅ Loaded 127276 instruments from smartapi_instruments_2025-09-14.csv
15:07:54 [api] INFO: TRADE: ✅ SmartAPI client initialized successfully (attempt 1)
15:07:54 [api] INFO: TRADE: ✅ Loaded 127276 instruments from smartapi_instruments_2025-09-14.csv
15:08:28 [api] INFO: TRADE: ✅ SmartAPI client initialized successfully (attempt 1)
15:08:28 [api] INFO: TRADE: ✅ Loaded 127276 instruments from smartapi_instruments_2025-09-14.csv
15:08:28 [api] WARNING: No instrument found for signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 15, 8, 28, 11906), quantity=1, price=None, stop_loss=None, entry_price=None, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
15:09:30 [api] INFO: TRADE: ✅ SmartAPI client initialized successfully (attempt 1)
15:09:31 [api] INFO: TRADE: ✅ Loaded 127276 instruments from smartapi_instruments_2025-09-14.csv
15:09:31 [api] WARNING: No instrument found for signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 15, 9, 30, 455243), quantity=1, price=None, stop_loss=None, entry_price=None, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
15:09:40 [trade] ERROR: Trading status check failed: name 'config' is not defined
15:10:32 [api] INFO: TRADE: ✅ SmartAPI client initialized successfully (attempt 1)
15:10:33 [api] INFO: TRADE: ✅ Loaded 127276 instruments from smartapi_instruments_2025-09-14.csv
15:10:57 [api] INFO: TRADE: ✅ SmartAPI client initialized successfully (attempt 1)
15:10:58 [api] INFO: TRADE: ✅ Loaded 127276 instruments from smartapi_instruments_2025-09-14.csv
15:10:58 [api] INFO: TRADE: ✅ Loaded 127276 instruments from smartapi_instruments_2025-09-14.csv
15:10:58 [trade] INFO: TRADE: ✅ SmartAPI connection verified successfully
15:10:58 [trade] INFO: TRADE: 💰 Available Cash: ₹0.2900
15:10:58 [main] INFO: TRADE: 🚀 Enhanced Algo Trading Bot Starting...
15:10:58 [main] INFO: TRADE: Trading Mode: SIMULATION
15:10:58 [main] INFO: TRADE: Default Lot Size: 1
15:10:58 [main] INFO: TRADE: Signal Timeout: 30s
15:10:59 [main] INFO: TRADE: ✅ Bot authenticated as Kaundinya_S_R
15:10:59 [main] INFO: TRADE: 🔍 Searching for group: ಆತ್ಮ ನಿರ್ಭರ
15:10:59 [main] INFO: TRADE: ✅ Found target group: ಆತ್ಮ ನಿರ್ಭರ
15:10:59 [main] INFO: TRADE: 🔄 Bot is now listening for trading signals...
15:11:59 [market_data] WARNING: No historical price data available for NIFTY after trying multiple dates
15:11:59 [market_data] WARNING: No historical price available for NIFTY
15:11:59 [trading_dataframes] WARNING: Could not retrieve market price for NIFTY
15:11:59 [api] INFO: TRADE: SIMULATION: BUY NIFTY at 163.8 (Entry: 155.4) - 1 lots
15:11:59 [api] INFO: TRADE: Trade logged to CSV: BUY NIFTY
15:11:59 [main] INFO: TRADE: ✅ Dataframe: Trade entry created for NIFTY 16 SEP 25000 CALL | API: SIMULATION: BUY order logged for NIFTY at 163.8
15:13:05 [main] ERROR: ❌ Invalid signal format. Supported formats:
1. ===Algo_Trading===$TRADE$BUY$SYMBOL DD MMM STRIKE PUT/CALL$PRICE$STOP_LOSS$TIMESTAMP
2. ===Algo_Trading===$INTIMATION$Continue to hold...
3. ===Algo_Trading===$TRADE$CLOSE$SYMBOL DD MMM STRIKE PUT/CALL$PRICE$TIMESTAMP$REASON
4. ===Algo_Trading===$Update$STOP LOSS to$PRICE$for option$SYMBOL...
5. Simple format: 'ACTION SYMBOL [STRIKE] [CE/PE]'
15:14:07 [market_data] WARNING: No historical price data available for NIFTY after trying multiple dates
15:14:07 [market_data] WARNING: No historical price available for NIFTY
15:14:07 [trading_dataframes] WARNING: Could not retrieve market price for NIFTY
15:14:07 [api] INFO: TRADE: SIMULATION: CLOSE NIFTY at 163.8 - 1 lots
15:14:07 [api] INFO: TRADE: Trade logged to CSV: CLOSE NIFTY
15:14:07 [main] INFO: TRADE: ✅ Dataframe: Trade completed and logged for NIFTY 16 SEP 25000 CALL | API: SIMULATION: Close order logged for NIFTY at 163.8
15:14:40 [trade] ERROR: Trading status check failed: name 'config' is not defined

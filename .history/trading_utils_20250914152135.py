#!/usr/bin/env python3
"""
Trading System Utilities
Command-line utilities for managing the trading system
"""

import sys
import os
import argparse
from datetime import datetime
import pandas as pd
from trading_config import config
from utils.signal_handler import SignalHandler
from utils.logger import logger

class TradingUtils:
    """Utility class for trading system management"""
    
    def __init__(self):
        self.signal_handler = SignalHandler()
        logger.setup(log_dir=config.LOG_DIR)
        self.logger = logger.get_logger('utils')
    
    def show_status(self):
        """Display current trading system status"""
        print("🔍 Trading System Status")
        print("=" * 50)
        
        # System configuration
        status = config.get_trading_status()
        print(f"Market Open: {'✅ YES' if status['market_open'] else '❌ NO'}")
        print(f"Trading Enabled: {'✅ YES' if status['trading_enabled'] else '❌ NO'}")
        print(f"Default Lot Size: {status['default_lot_size']}")
        print(f"Signal Timeout: {status['signal_timeout']}s")
        print(f"Max Positions: {status['max_positions']}")
        print(f"Auto Close: {'✅ ON' if status['auto_close_enabled'] else '❌ OFF'}")
        print(f"CSV Logging: {'✅ ON' if status['csv_logging'] else '❌ OFF'}")
        
        # Active positions
        positions = self.signal_handler.get_active_positions()
        if positions.get('status') == 'success':
            pos_data = positions['data']
            print(f"\nActive Positions: {pos_data['total_positions']}")
            
            if pos_data['positions']:
                print("\nPosition Details:")
                for pos in pos_data['positions']:
                    print(f"  • {pos['symbol']} - {pos['action']} {pos['lots']} lots @ {pos['entry_price']}")
        
        print("\n" + "=" * 50)
    
    def show_logs(self, lines=20):
        """Display recent log entries"""
        try:
            today = datetime.now().strftime('%Y-%m-%d')
            log_file = os.path.join(config.LOG_DIR, f'trading_{today}.log')
            
            if not os.path.exists(log_file):
                print(f"❌ Log file not found: {log_file}")
                return
            
            print(f"📋 Recent Log Entries (last {lines} lines)")
            print("=" * 80)
            
            with open(log_file, 'r') as f:
                log_lines = f.readlines()
                for line in log_lines[-lines:]:
                    print(line.strip())
            
            print("=" * 80)
            
        except Exception as e:
            print(f"❌ Error reading logs: {e}")
    
    def show_trades(self, date=None):
        """Display trade history for a specific date"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')

            # Try new date-based folder structure first
            date_dir = config.get_date_based_trade_log_dir(date)
            trade_file = os.path.join(date_dir, f'trades_{date}.csv')

            # Fallback to old structure for backward compatibility
            if not os.path.exists(trade_file):
                trade_file = os.path.join(config.TRADE_LOG_DIR, f'trades_{date}.csv')

            if not os.path.exists(trade_file):
                print(f"❌ No trades found for {date}")
                return
            
            df = pd.read_csv(trade_file)
            
            print(f"📊 Trade History for {date}")
            print("=" * 100)
            print(df.to_string(index=False))
            print("=" * 100)
            print(f"Total Trades: {len(df)}")
            
            # Summary statistics
            if not df.empty:
                buy_trades = len(df[df['action'] == 'BUY'])
                sell_trades = len(df[df['action'] == 'SELL'])
                print(f"BUY Orders: {buy_trades}")
                print(f"SELL Orders: {sell_trades}")
            
        except Exception as e:
            print(f"❌ Error reading trades: {e}")
    
    def force_close_all(self):
        """Force close all active positions"""
        print("⚠️  Force closing all active positions...")
        
        try:
            results = self.signal_handler.force_close_all_positions()
            
            if not results:
                print("ℹ️  No active positions to close")
                return
            
            for result in results:
                if result.get('status') == 'success':
                    print(f"✅ {result.get('message', 'Position closed')}")
                else:
                    print(f"❌ {result.get('message', 'Failed to close position')}")
            
            print(f"🔒 Attempted to close {len(results)} positions")
            
        except Exception as e:
            print(f"❌ Error during force close: {e}")
    
    def test_signal(self, signal_text):
        """Test a signal without executing trades"""
        print(f"🧪 Testing Signal: {signal_text}")
        print("=" * 80)
        
        # Temporarily disable trading
        original_trading = config.ENABLE_ACTUAL_TRADING
        config.ENABLE_ACTUAL_TRADING = False
        
        try:
            import asyncio
            result = asyncio.run(self.signal_handler.handle_telegram_message(signal_text))
            
            print(f"Status: {result.get('status', 'unknown')}")
            print(f"Message: {result.get('message', 'No message')}")
            
            if 'dataframe_result' in result:
                df_result = result['dataframe_result']
                print(f"Dataframe Status: {df_result.get('status', 'unknown')}")
                print(f"Dataframe Message: {df_result.get('message', 'No message')}")
            
        except Exception as e:
            print(f"❌ Error testing signal: {e}")
        finally:
            # Restore original setting
            config.ENABLE_ACTUAL_TRADING = original_trading
        
        print("=" * 80)
    
    def cleanup_logs(self, days=7):
        """Clean up old log files"""
        try:
            import glob
            from datetime import timedelta
            
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Clean up log files
            log_pattern = os.path.join(config.LOG_DIR, 'trading_*.log')
            trade_pattern = os.path.join(config.TRADE_LOG_DIR, '*.csv')
            
            cleaned_files = 0
            
            for pattern in [log_pattern, trade_pattern]:
                for file_path in glob.glob(pattern):
                    file_date = datetime.fromtimestamp(os.path.getctime(file_path))
                    if file_date < cutoff_date:
                        os.remove(file_path)
                        cleaned_files += 1
                        print(f"🗑️  Removed: {os.path.basename(file_path)}")
            
            print(f"✅ Cleaned up {cleaned_files} old files (older than {days} days)")
            
        except Exception as e:
            print(f"❌ Error during cleanup: {e}")

def main():
    """Main CLI interface"""
    parser = argparse.ArgumentParser(description='Trading System Utilities')
    parser.add_argument('command', choices=[
        'status', 'logs', 'trades', 'close-all', 'test', 'cleanup'
    ], help='Command to execute')
    
    parser.add_argument('--lines', type=int, default=20, help='Number of log lines to show')
    parser.add_argument('--date', type=str, help='Date for trade history (YYYY-MM-DD)')
    parser.add_argument('--signal', type=str, help='Signal text to test')
    parser.add_argument('--days', type=int, default=7, help='Days to keep for cleanup')
    
    args = parser.parse_args()
    
    utils = TradingUtils()
    
    try:
        if args.command == 'status':
            utils.show_status()
        elif args.command == 'logs':
            utils.show_logs(args.lines)
        elif args.command == 'trades':
            utils.show_trades(args.date)
        elif args.command == 'close-all':
            utils.force_close_all()
        elif args.command == 'test':
            if not args.signal:
                print("❌ Please provide a signal to test with --signal")
                sys.exit(1)
            utils.test_signal(args.signal)
        elif args.command == 'cleanup':
            utils.cleanup_logs(args.days)
    
    except KeyboardInterrupt:
        print("\n🛑 Operation cancelled by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Debug script to test NIFTY instrument lookup
"""

import pandas as pd
import os
from utils.api_handler import APIHandler, TradeSignal

def test_nifty_lookup():
    print("🔍 Testing NIFTY instrument lookup...")
    
    # Initialize API handler
    api = APIHandler()
    
    # Check if instrument data is loaded
    if api.instrument_df is None or api.instrument_df.empty:
        print("❌ No instrument data loaded")
        return False
    
    print(f"✅ Loaded {len(api.instrument_df)} instruments")
    print(f"📊 Columns: {list(api.instrument_df.columns)}")
    
    # Check column mapping
    col_map = api._get_column_mapping()
    print(f"🗺️ Column mapping: {col_map}")
    
    # Check if required columns exist
    for key, col_name in col_map.items():
        if col_name not in api.instrument_df.columns:
            print(f"❌ Missing column: {col_name} for {key}")
        else:
            print(f"✅ Found column: {col_name} for {key}")
    
    # Look for NIFTY specifically
    print("\n🔍 Searching for NIFTY instruments...")
    
    # Search by name column
    name_col = col_map['custom_symbol']
    nifty_matches = api.instrument_df[api.instrument_df[name_col] == 'NIFTY']
    print(f"📊 Found {len(nifty_matches)} exact matches for 'NIFTY' in {name_col} column")
    
    if not nifty_matches.empty:
        print("✅ NIFTY instruments found:")
        for idx, row in nifty_matches.head(5).iterrows():
            print(f"  • Token: {row[col_map['token']]}, Symbol: {row[col_map['tradingsymbol']]}, Name: {row[col_map['custom_symbol']]}, Exchange: {row[col_map['exchange']]}")
    
    # Test with regex pattern (current logic)
    print(f"\n🔍 Testing regex pattern for NIFTY...")
    regex_matches = api.instrument_df[api.instrument_df[name_col].str.contains(f"^NIFTY$", na=False, regex=True)]
    print(f"📊 Found {len(regex_matches)} regex matches for '^NIFTY$' pattern")
    
    if not regex_matches.empty:
        print("✅ NIFTY regex matches found:")
        for idx, row in regex_matches.head(5).iterrows():
            print(f"  • Token: {row[col_map['token']]}, Symbol: {row[col_map['tradingsymbol']]}, Name: {row[col_map['custom_symbol']]}, Exchange: {row[col_map['exchange']]}")
    
    # Test the actual find_instrument method with index signal
    print(f"\n🎯 Testing find_instrument method with INDEX signal...")
    test_signal = TradeSignal(
        symbol="NIFTY",
        action="BUY",
        signal_type="TRADE"
    )

    instrument = api.find_instrument(test_signal)
    if instrument:
        print("✅ find_instrument succeeded for INDEX:")
        print(f"  • Token: {instrument['token']}")
        print(f"  • Trading Symbol: {instrument['tradingsymbol']}")
        print(f"  • Exchange: {instrument['exchange']}")
        print(f"  • Custom Symbol: {instrument['custom_symbol']}")
    else:
        print("❌ find_instrument failed for INDEX")

    # Test with the actual OPTIONS signal from the log
    print(f"\n🎯 Testing find_instrument method with OPTIONS signal...")
    options_signal = TradeSignal(
        symbol="NIFTY",
        action="BUY",
        strike=25000.0,
        option_type="CE",
        expiry_date="16 SEP",
        signal_type="TRADE"
    )

    options_instrument = api.find_instrument(options_signal)
    if options_instrument:
        print("✅ find_instrument succeeded for OPTIONS:")
        print(f"  • Token: {options_instrument['token']}")
        print(f"  • Trading Symbol: {options_instrument['tradingsymbol']}")
        print(f"  • Exchange: {options_instrument['exchange']}")
        print(f"  • Custom Symbol: {options_instrument['custom_symbol']}")
    else:
        print("❌ find_instrument failed for OPTIONS")
        print("🔍 Let's debug the options lookup logic...")

        # Debug the expected trading symbol format
        from datetime import datetime
        expiry_parts = options_signal.expiry_date.split()
        day, month = expiry_parts
        current_year = datetime.now().year
        formatted_expiry = f"{day}{month}{current_year}"
        expected_trading_symbol = f"{options_signal.symbol}{formatted_expiry}{int(options_signal.strike * 100)}{options_signal.option_type}"
        print(f"🔍 Expected trading symbol: {expected_trading_symbol}")

        # Search for the specific option with correct strike in paise
        nifty_options = api.instrument_df[
            (api.instrument_df['name'] == 'NIFTY') &
            (api.instrument_df['strike'] == options_signal.strike * 100) &  # Convert to paise
            (api.instrument_df['instrumenttype'] == 'OPTIDX') &
            (api.instrument_df['symbol'].str.contains('16SEP2025', na=False)) &
            (api.instrument_df['symbol'].str.contains('CE', na=False))
        ]

        print(f"📊 Found {len(nifty_options)} matching NIFTY 25000 CE 16SEP options")
        if not nifty_options.empty:
            for idx, row in nifty_options.head(3).iterrows():
                print(f"  • Token: {row['token']}, Symbol: {row['symbol']}, Strike: {row['strike']}")
                print(f"    Expiry: {row['expiry']}, Type: {row['instrumenttype']}")

        # Also check what the actual symbol format is
        print(f"\n🔍 Checking actual NIFTY 16SEP options format...")
        sample_options = api.instrument_df[
            (api.instrument_df['name'] == 'NIFTY') &
            (api.instrument_df['symbol'].str.contains('16SEP2025', na=False)) &
            (api.instrument_df['instrumenttype'] == 'OPTIDX')
        ].head(5)

        for idx, row in sample_options.iterrows():
            print(f"  • Symbol: {row['symbol']}, Strike: {row['strike']}, Expiry: {row['expiry']}")

    return instrument is not None or options_instrument is not None

if __name__ == "__main__":
    success = test_nifty_lookup()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}")

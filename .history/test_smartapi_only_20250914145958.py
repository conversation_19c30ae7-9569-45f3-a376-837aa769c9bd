#!/usr/bin/env python3
"""
Test SmartAPI-only implementation and clean up legacy files
"""

import os
from utils.api_handler import APIHandler
from utils.instrument_updater import InstrumentUpdater
from utils.logger import logger

def test_smartapi_only():
    """Test the SmartAPI-only implementation"""
    print("🔄 Testing SmartAPI-Only Implementation...")
    print("=" * 60)
    
    # Step 1: Clean up legacy files
    print("🧹 Step 1: Cleaning up legacy files...")
    deps_dir = "Dependencies"
    if os.path.exists(deps_dir):
        legacy_files = [f for f in os.listdir(deps_dir) if f.startswith('all_instrument') and f.endswith('.csv')]
        for file in legacy_files:
            file_path = os.path.join(deps_dir, file)
            try:
                os.remove(file_path)
                print(f"  🗑️ Removed legacy file: {file}")
            except Exception as e:
                print(f"  ❌ Could not remove {file}: {e}")
    
    # Step 2: Force download fresh SmartAPI data
    print("\n📥 Step 2: Downloading fresh SmartAPI data...")
    updater = InstrumentUpdater()
    success = updater.update_instruments(force_update=True)
    
    if not success:
        print("❌ Failed to download SmartAPI instruments")
        return False
    
    # Step 3: Test API Handler with SmartAPI format
    print("\n📊 Step 3: Testing API Handler with SmartAPI format...")
    api = APIHandler()
    
    if len(api.instrument_df) == 0:
        print("❌ API Handler failed to load instruments")
        return False
    
    print(f"✅ Loaded {len(api.instrument_df)} instruments")
    print(f"📊 Columns: {list(api.instrument_df.columns)}")
    
    # Step 4: Test column mapping
    print("\n🔍 Step 4: Testing column mapping...")
    col_map = api._get_column_mapping()
    print(f"Column mapping: {col_map}")
    
    # Step 5: Test instrument finding
    print("\n🎯 Step 5: Testing instrument finding...")
    from utils.api_handler import TradeSignal
    
    # Test with NIFTY index
    test_signal = TradeSignal(
        symbol="NIFTY",
        action="BUY"
    )
    
    instrument = api.find_instrument(test_signal)
    if instrument:
        print(f"✅ Found NIFTY instrument:")
        print(f"  • Symbol: {instrument['tradingsymbol']}")
        print(f"  • Token: {instrument['token']}")
        print(f"  • Exchange: {instrument['exchange']}")
    else:
        print("❌ Could not find NIFTY instrument")
    
    # Step 6: Show final directory status
    print("\n📂 Step 6: Final Dependencies directory:")
    if os.path.exists(deps_dir):
        files = [f for f in os.listdir(deps_dir) if f.endswith('.csv')]
        for file in sorted(files):
            file_path = os.path.join(deps_dir, file)
            size_mb = os.path.getsize(file_path) / (1024 * 1024)
            print(f"  • {file} ({size_mb:.2f} MB)")
    
    print("\n" + "=" * 60)
    print("✅ SmartAPI-Only Implementation Test Complete!")
    return True

if __name__ == "__main__":
    success = test_smartapi_only()
    if success:
        print("🎉 All tests passed! System is now using SmartAPI format only.")
    else:
        print("❌ Some tests failed. Please check the logs.")

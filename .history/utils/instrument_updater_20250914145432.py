import os
import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import zipfile
import io
from .logger import logger
from trading_config import config


class InstrumentUpdater:
    """
    Automatic instrument file updater for SmartAPI
    Downloads and updates instrument files daily to ensure latest tokens
    """
    
    def __init__(self, smart_api=None):
        self.logger = logger.get_logger('instrument_updater')
        self.smart_api = smart_api
        self.dependencies_dir = config.INSTRUMENT_FILE_PATH
        self.base_url = "https://margincalculator.angelbroking.com/OpenAPI_File/files/OpenAPIScripMaster.json"
        
        # Ensure dependencies directory exists
        self._ensure_dependencies_dir()
        self.logger.info("Instrument Updater initialized")
    
    def _ensure_dependencies_dir(self):
        """Ensure the Dependencies directory exists"""
        if not os.path.exists(self.dependencies_dir):
            os.makedirs(self.dependencies_dir)
            self.logger.info(f"Created dependencies directory: {self.dependencies_dir}")
    
    def _get_today_filename(self) -> str:
        """Generate today's instrument filename"""
        today = datetime.now().strftime("%Y-%m-%d")
        return os.path.join(self.dependencies_dir, f"smartapi_instruments_{today}.csv")
    
    def _get_latest_local_file(self) -> Optional[str]:
        """Get the latest local instrument file - prioritize SmartAPI format"""
        try:
            import glob
            current_date = datetime.now().strftime("%Y-%m-%d")

            # First priority: Today's SmartAPI format file
            smartapi_today = os.path.join(self.dependencies_dir, f"smartapi_instruments_{current_date}.csv")
            if os.path.exists(smartapi_today):
                return smartapi_today

            # Second priority: Any SmartAPI format file
            smartapi_pattern = os.path.join(self.dependencies_dir, "smartapi_instruments_*.csv")
            smartapi_files = glob.glob(smartapi_pattern)
            if smartapi_files:
                return max(smartapi_files, key=os.path.getctime)

            # Third priority: Legacy format files
            legacy_pattern = os.path.join(self.dependencies_dir, "all_instrument*.csv")
            legacy_files = glob.glob(legacy_pattern)
            if legacy_files:
                return max(legacy_files, key=os.path.getctime)

            return None
        except Exception as e:
            self.logger.error(f"Error finding latest local file: {e}")
            return None
    
    def _is_update_needed(self) -> bool:
        """Check if instrument file update is needed"""
        try:
            today_file = self._get_today_filename()
            
            # If today's file already exists, no update needed
            if os.path.exists(today_file):
                self.logger.info("Today's instrument file already exists")
                return False
            
            # If no local files exist, update needed
            latest_file = self._get_latest_local_file()
            if not latest_file:
                self.logger.info("No local instrument files found, update needed")
                return True
            
            # Check if latest file is older than today
            file_date = os.path.getctime(latest_file)
            file_datetime = datetime.fromtimestamp(file_date)
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            
            if file_datetime < today:
                self.logger.info("Local instrument file is outdated, update needed")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking update requirement: {e}")
            return True  # Default to update if check fails
    
    def download_instruments_from_api(self) -> Optional[pd.DataFrame]:
        """Download instrument data using SmartAPI"""
        try:
            if not self.smart_api:
                self.logger.error("SmartAPI not initialized")
                return None

            # SmartAPI doesn't have a direct method to get all instruments
            # We'll use searchScrip with common symbols to get some data
            # But this is not ideal for getting all instruments
            self.logger.warning("SmartAPI doesn't provide a direct method to download all instruments")
            self.logger.info("Falling back to public URL download method")
            return None

        except Exception as e:
            self.logger.error(f"Error downloading instruments from API: {e}")
            return None
    
    def download_instruments_from_url(self) -> Optional[pd.DataFrame]:
        """Download instrument data from public URL with multiple fallback URLs"""
        urls = [
            "https://margincalculator.angelbroking.com/OpenAPI_File/files/OpenAPIScripMaster.json",
            "https://margincalculator.angelbroking.com/OpenAPI/files/OpenAPIScripMaster.json",
            self.base_url  # Original fallback URL
        ]

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

        for url in urls:
            try:
                self.logger.info(f"Attempting to download instruments from: {url}")

                response = requests.get(url, headers=headers, timeout=30)
                response.raise_for_status()

                # Parse JSON data
                import json
                instruments_data = json.loads(response.text)

                if instruments_data:
                    df = pd.DataFrame(instruments_data)
                    self.logger.info(f"✅ Downloaded {len(df)} instruments from {url}")
                    self.logger.info(f"📊 Columns: {list(df.columns)}")
                    return df

            except Exception as e:
                self.logger.warning(f"Failed to fetch from {url}: {e}")
                continue

        self.logger.error("Failed to download instruments from all URLs")
        return None
    
    def process_instrument_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process and standardize instrument data"""
        try:
            # Standardize column names to match existing format
            column_mapping = {
                'token': 'SEM_SMST_SECURITY_ID',
                'symbol': 'SEM_TRADING_SYMBOL',
                'name': 'SEM_CUSTOM_SYMBOL',
                'expiry': 'SEM_EXPIRY_DATE',
                'strike': 'SEM_STRIKE_PRICE',
                'lotsize': 'SEM_LOT_SIZE',
                'instrumenttype': 'SEM_INSTRUMENT_NAME',
                'exch_seg': 'SEM_EXM_EXCH_ID',
                'tick_size': 'SEM_TICK_SIZE'
            }
            
            # Rename columns if they exist
            for old_name, new_name in column_mapping.items():
                if old_name in df.columns:
                    df = df.rename(columns={old_name: new_name})
            
            # Add missing columns with default values if needed
            required_columns = [
                'SEM_SMST_SECURITY_ID', 'SEM_TRADING_SYMBOL', 'SEM_CUSTOM_SYMBOL',
                'SEM_EXM_EXCH_ID', 'SEM_STRIKE_PRICE', 'SEM_OPTION_TYPE'
            ]
            
            for col in required_columns:
                if col not in df.columns:
                    df[col] = ''
            
            # Process option types
            if 'SEM_OPTION_TYPE' in df.columns:
                df['SEM_OPTION_TYPE'] = df['SEM_OPTION_TYPE'].replace({
                    'CALL': 'CE',
                    'PUT': 'PE'
                })
            
            self.logger.info("Instrument data processed successfully")
            return df
            
        except Exception as e:
            self.logger.error(f"Error processing instrument data: {e}")
            return df
    
    def save_instrument_file(self, df: pd.DataFrame) -> bool:
        """Save instrument data to CSV file with enhanced logging"""
        try:
            filename = self._get_today_filename()
            df.to_csv(filename, index=False)

            # Get file size for logging
            file_size = os.path.getsize(filename) / (1024 * 1024)  # MB

            self.logger.info(f"✅ Saved instrument file: {os.path.basename(filename)}")
            self.logger.info(f"📊 File size: {file_size:.2f} MB")
            self.logger.info(f"📈 Total instruments: {len(df)}")
            return True

        except Exception as e:
            self.logger.error(f"Error saving instrument file: {e}")
            return False
    
    def cleanup_old_files(self, keep_days: int = 7):
        """Remove old instrument files to save space - Enhanced with better cleanup"""
        try:
            import glob
            from datetime import datetime, timedelta

            # Get current date for comparison
            current_date = datetime.now().strftime("%Y-%m-%d")

            # Clean up both old and new naming patterns
            patterns = [
                os.path.join(self.dependencies_dir, "smartapi_instruments_*.csv"),
                os.path.join(self.dependencies_dir, "all_instrument*.csv")
            ]

            files_removed = 0
            for pattern in patterns:
                files = glob.glob(pattern)

                for file_path in files:
                    filename = os.path.basename(file_path)

                    # Don't remove today's file
                    if current_date in filename:
                        continue

                    try:
                        file_date = datetime.fromtimestamp(os.path.getctime(file_path))
                        cutoff_date = datetime.now() - timedelta(days=keep_days)

                        if file_date < cutoff_date:
                            os.remove(file_path)
                            self.logger.info(f"🗑️ Removed old instrument file: {filename}")
                            files_removed += 1
                    except Exception as file_error:
                        self.logger.warning(f"Could not remove {filename}: {file_error}")

            if files_removed > 0:
                self.logger.info(f"🧹 Cleanup complete: {files_removed} old files removed")
            else:
                self.logger.info("🧹 No old files to clean up")

        except Exception as e:
            self.logger.error(f"Error cleaning up old files: {e}")

    def _cleanup_old_smartapi_files(self, current_date: str):
        """Clean up old SmartAPI instrument files, keeping only today's file"""
        try:
            if not os.path.exists(self.dependencies_dir):
                return

            files_removed = 0
            for item in os.listdir(self.dependencies_dir):
                if (item.startswith('smartapi_instruments_') or item.startswith('all_instrument')) and item.endswith('.csv'):
                    # Don't remove today's file
                    if current_date in item:
                        continue

                    file_path = os.path.join(self.dependencies_dir, item)
                    if os.path.isfile(file_path):
                        try:
                            os.remove(file_path)
                            self.logger.info(f"🗑️ Removed old instrument file: {item}")
                            files_removed += 1
                        except Exception as file_error:
                            self.logger.warning(f"Could not remove {item}: {file_error}")

            if files_removed > 0:
                self.logger.info(f"🧹 Cleanup complete: {files_removed} old files removed")

        except Exception as e:
            self.logger.error(f"Error cleaning up old SmartAPI files: {e}")

    def update_instruments(self, force_update: bool = False) -> bool:
        """
        Enhanced SmartAPI instrument update with proper JSON format handling
        """
        try:
            current_date = datetime.now().strftime("%Y-%m-%d")
            expected_file = f'smartapi_instruments_{current_date}.csv'
            expected_file_path = os.path.join(self.dependencies_dir, expected_file)

            # Clean up old files first
            self._cleanup_old_smartapi_files(current_date)

            # Check if today's file already exists and force_update is False
            if not force_update and os.path.exists(expected_file_path):
                self.logger.info(f"📁 Using existing SmartAPI instrument file: {expected_file}")
                return True

            self.logger.info("📥 Fetching fresh SmartAPI instrument data...")

            # Enhanced URLs for SmartAPI JSON format
            urls = [
                "https://margincalculator.angelbroking.com/OpenAPI_File/files/OpenAPIScripMaster.json",
                "https://margincalculator.angelbroking.com/OpenAPI/files/OpenAPIScripMaster.json"
            ]

            df = None
            for url in urls:
                try:
                    self.logger.info(f"🔄 Attempting download from: {url}")

                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }

                    response = requests.get(url, headers=headers, timeout=30)
                    response.raise_for_status()

                    # Parse JSON data directly to DataFrame
                    import json
                    instruments_data = json.loads(response.text)

                    if instruments_data:
                        df = pd.DataFrame(instruments_data)
                        self.logger.info(f"✅ Downloaded {len(df)} instruments from SmartAPI")
                        self.logger.info(f"📊 SmartAPI Columns: {list(df.columns)}")
                        break

                except Exception as e:
                    self.logger.warning(f"Failed to fetch from {url}: {e}")
                    continue

            if df is None or df.empty:
                self.logger.error("❌ Failed to download instruments from all SmartAPI URLs")
                return False

            # Save with proper naming
            df.to_csv(expected_file_path, index=False)

            # Get file size for logging
            file_size_mb = os.path.getsize(expected_file_path) / (1024 * 1024)

            self.logger.info(f"✅ Saved SmartAPI instrument file: {expected_file}")
            self.logger.info(f"📊 File size: {file_size_mb:.2f} MB")
            self.logger.info(f"📈 Total instruments: {len(df)}")

            return True

        except Exception as e:
            self.logger.error(f"Error updating SmartAPI instruments: {e}")
            return False
    
    def get_update_status(self) -> Dict[str, Any]:
        """Get current update status information"""
        try:
            latest_file = self._get_latest_local_file()
            today_file = self._get_today_filename()
            
            status = {
                "latest_file": latest_file,
                "today_file_exists": os.path.exists(today_file),
                "update_needed": self._is_update_needed(),
                "dependencies_dir": self.dependencies_dir
            }
            
            if latest_file:
                file_date = datetime.fromtimestamp(os.path.getctime(latest_file))
                status["latest_file_date"] = file_date.strftime("%Y-%m-%d %H:%M:%S")
                
                # Get file size
                file_size = os.path.getsize(latest_file)
                status["latest_file_size_mb"] = round(file_size / (1024 * 1024), 2)
            
            return status
            
        except Exception as e:
            self.logger.error(f"Error getting update status: {e}")
            return {"error": str(e)}

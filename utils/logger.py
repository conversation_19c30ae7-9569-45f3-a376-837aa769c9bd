import logging
import os
from datetime import datetime

class Logger:
    _instance = None
    _loggers = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def setup(self, log_dir="logs"):
        """Setup simplified logging configuration with single daily log file in date-based folder"""
        # Create base log directory
        os.makedirs(log_dir, exist_ok=True)

        # Create date-based subdirectory
        today = datetime.now().strftime('%Y-%m-%d')
        date_log_dir = os.path.join(log_dir, today)
        os.makedirs(date_log_dir, exist_ok=True)

        # Single daily log file in date-based folder
        daily_log_file = os.path.join(date_log_dir, f'trading_{today}.log')

        # Setup all loggers to use the same file
        self._setup_logger('main', daily_log_file)
        self._setup_logger('trade', daily_log_file)
        self._setup_logger('api', daily_log_file)

    def _setup_logger(self, name, filepath):
        """Setup individual logger with file and console handlers - optimized for essential logs only"""
        logger = logging.getLogger(name)
        logger.setLevel(logging.WARNING)  # Only log warnings and errors by default

        # Clear any existing handlers
        logger.handlers = []

        # Create formatter - simplified format
        formatter = logging.Formatter(
            '%(asctime)s [%(name)s] %(levelname)s: %(message)s',
            datefmt='%H:%M:%S'
        )

        # File handler
        file_handler = logging.FileHandler(filepath)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # Console handler - only for errors
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.ERROR)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

        self._loggers[name] = logger
        return logger

    def get_logger(self, name):
        """Get logger by name. Creates new logger if it doesn't exist."""
        if name not in self._loggers:
            today = datetime.now().strftime('%Y-%m-%d')
            date_log_dir = os.path.join("logs", today)
            os.makedirs(date_log_dir, exist_ok=True)
            log_file_path = os.path.join(date_log_dir, f"trading_{today}.log")
            self._setup_logger(name, log_file_path)
        return self._loggers[name]

    def log_trade_event(self, name, message):
        """Log important trade events at INFO level"""
        logger = self.get_logger(name)
        logger.setLevel(logging.INFO)  # Temporarily enable INFO for trade events
        logger.info(f"TRADE: {message}")
        logger.setLevel(logging.WARNING)  # Reset to WARNING

    def log_error(self, name, message):
        """Log errors"""
        logger = self.get_logger(name)
        logger.error(message)

logger = Logger()  # Singleton instance
